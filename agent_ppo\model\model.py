#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from typing import List
import torch
from torch import nn
import torch.nn.functional as F
import numpy as np
from agent_ppo.conf.conf import Config

import sys
import os

if os.path.basename(sys.argv[0]) == "learner.py":
    import torch

    torch.set_num_interop_threads(2)
    torch.set_num_threads(2)
else:
    import torch

    torch.set_num_interop_threads(4)
    torch.set_num_threads(4)


class CNNFeatureExtractor(nn.Module):
    """CNN特徵提取器，用於處理11x11的局部視野地圖"""

    def __init__(self, input_channels=5, output_dim=64):
        super(CNNFeatureExtractor, self).__init__()

        # 輸入: (batch_size, 5, 11, 11) - 5個通道的11x11地圖
        # 通道: obstacle, end_point, treasure, buff, memory_map

        # 第一層卷積: 11x11 -> 9x9
        self.conv1 = nn.Conv2d(input_channels, 16, kernel_size=3, stride=1, padding=0)
        self.bn1 = nn.BatchNorm2d(16)

        # 第二層卷積: 9x9 -> 7x7
        self.conv2 = nn.Conv2d(16, 32, kernel_size=3, stride=1, padding=0)
        self.bn2 = nn.BatchNorm2d(32)

        # 第三層卷積: 7x7 -> 5x5
        self.conv3 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=0)
        self.bn3 = nn.BatchNorm2d(64)

        # 全局平均池化: 5x5 -> 1x1
        self.global_avg_pool = nn.AdaptiveAvgPool2d(1)

        # 全連接層
        self.fc = nn.Linear(64, output_dim)

        # Dropout
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        # x shape: (batch_size, 5, 11, 11)

        # 第一層卷積 + BN + ReLU
        x = F.relu(self.bn1(self.conv1(x)))  # (batch_size, 16, 9, 9)

        # 第二層卷積 + BN + ReLU
        x = F.relu(self.bn2(self.conv2(x)))  # (batch_size, 32, 7, 7)

        # 第三層卷積 + BN + ReLU
        x = F.relu(self.bn3(self.conv3(x)))  # (batch_size, 64, 5, 5)

        # 全局平均池化
        x = self.global_avg_pool(x)  # (batch_size, 64, 1, 1)

        # 展平
        x = x.view(x.size(0), -1)  # (batch_size, 64)

        # Dropout + 全連接
        x = self.dropout(x)
        x = self.fc(x)  # (batch_size, output_dim)

        return x


class NetworkModelBase(nn.Module):
    def __init__(self):
        super().__init__()
        # feature configure parameter
        # 特征配置参数
        self.data_split_shape = Config.DATA_SPLIT_SHAPE
        self.feature_split_shape = Config.FEATURE_SPLIT_SHAPE
        self.label_size = Config.ACTION_NUM
        self.feature_len = Config.FEATURE_LEN
        self.value_num = Config.VALUE_NUM

        self.var_beta = Config.BETA_START
        self.vf_coef = Config.VF_COEF

        self.clip_param = Config.CLIP_PARAM

        self.data_len = Config.data_len

        # CNN特徵提取器 - 移到模型內部以支持端到端訓練
        self.cnn_feature_extractor = CNNFeatureExtractor(input_channels=5, output_dim=64)

        # 非地圖特徵維度: 2+6+6+6+3 = 23維
        self.non_map_feature_dim = 23

        # 總特徵維度: 非地圖特徵 + CNN特徵
        self.total_feature_dim = self.non_map_feature_dim + 64

        # Main MLP network - 使用總特徵維度
        self.main_fc_dim_list = [self.total_feature_dim, 128, 256]
        self.main_mlp_net = MLP(self.main_fc_dim_list, "main_mlp_net", non_linearity_last=True)
        self.label_mlp = MLP([256, 64, self.label_size], "label_mlp")
        self.value_mlp = MLP([256, 64, self.value_num], "value_mlp")

    def process_legal_action(self, label, legal_action):
        label_max, _ = torch.max(label * legal_action, 1, True)
        label = label - label_max
        label = label * legal_action
        label = label + 1e5 * (legal_action - 1)
        return label

    def forward(self, feature_dict, legal_action):
        # 提取地圖特徵並通過CNN處理
        map_features = feature_dict['map_features']  # Shape: (batch_size, 5, 11, 11)
        cnn_features = self.cnn_feature_extractor(map_features)  # Shape: (batch_size, 64)

        # 提取非地圖特徵
        non_map_features = feature_dict['non_map_features']  # Shape: (batch_size, 23)

        # 連接所有特徵
        combined_features = torch.cat([non_map_features, cnn_features], dim=1)  # Shape: (batch_size, 87)

        # Main MLP processing
        # 主MLP处理
        fc_mlp_out = self.main_mlp_net(combined_features)

        # Action and value processing
        # 处理动作和值
        label_mlp_out = self.label_mlp(fc_mlp_out)
        label_out = self.process_legal_action(label_mlp_out, legal_action)

        prob = torch.nn.functional.softmax(label_out, dim=1)
        value = self.value_mlp(fc_mlp_out)

        return prob, value


class NetworkModelActor(NetworkModelBase):
    def format_data(self, feature_dict, legal_action):
        # 將特徵字典轉換為tensor格式
        formatted_feature_dict = {
            'map_features': torch.tensor(feature_dict['map_features']).to(torch.float32),
            'non_map_features': torch.tensor(feature_dict['non_map_features']).to(torch.float32)
        }
        return (
            formatted_feature_dict,
            torch.tensor(legal_action).to(torch.float32),
        )


class NetworkModelLearner(NetworkModelBase):
    def format_data(self, datas):
        return datas.view(-1, self.data_len).float().split(self.data_split_shape, dim=1)

    def _reconstruct_feature_dict(self, serialized_features):
        """從序列化的特徵重新構建特徵字典"""
        batch_size = serialized_features.shape[0]

        # 分離地圖特徵和非地圖特徵
        map_features_flat = serialized_features[:, :605]  # 前605維是地圖特徵 (5*11*11)
        non_map_features = serialized_features[:, 605:]   # 後23維是非地圖特徵

        # 重塑地圖特徵: (batch_size, 605) -> (batch_size, 5, 11, 11)
        map_features = map_features_flat.view(batch_size, 5, 11, 11)

        return {
            'map_features': map_features,
            'non_map_features': non_map_features
        }

    def forward(self, data_list, inference=False):
        serialized_features = data_list[0]
        legal_action = data_list[-1]

        # 重新構建特徵字典
        feature_dict = self._reconstruct_feature_dict(serialized_features)

        return super().forward(feature_dict, legal_action)


def make_fc_layer(in_features: int, out_features: int):
    # Wrapper function to create and initialize a linear layer
    # 创建并初始化一个线性层
    fc_layer = nn.Linear(in_features, out_features)

    # initialize weight and bias
    # 初始化权重及偏移量
    nn.init.orthogonal(fc_layer.weight)
    nn.init.zeros_(fc_layer.bias)

    return fc_layer


class MLP(nn.Module):
    def __init__(
        self,
        fc_feat_dim_list: List[int],
        name: str,
        non_linearity: nn.Module = nn.ReLU,
        non_linearity_last: bool = False,
    ):
        # Create a MLP object
        # 创建一个 MLP 对象
        super().__init__()
        self.fc_layers = nn.Sequential()
        for i in range(len(fc_feat_dim_list) - 1):
            fc_layer = make_fc_layer(fc_feat_dim_list[i], fc_feat_dim_list[i + 1])
            self.fc_layers.add_module("{0}_fc{1}".format(name, i + 1), fc_layer)
            # no relu for the last fc layer of the mlp unless required
            # 除非有需要，否则 mlp 的最后一个 fc 层不使用 relu
            if i + 1 < len(fc_feat_dim_list) - 1 or non_linearity_last:
                self.fc_layers.add_module("{0}_non_linear{1}".format(name, i + 1), non_linearity())

    def forward(self, data):
        return self.fc_layers(data)
