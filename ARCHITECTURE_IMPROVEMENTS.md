# PPO架構改進：解耦CNN實現端到端訓練

## 問題分析

### 原始架構問題
1. **梯度流被切斷**: CNN在Preprocessor中執行並轉換為numpy，無法進行端到端訓練
2. **狀態信息缺失**: flash_cooldown等關鍵狀態沒有作為特徵輸入給模型
3. **動作處理非最佳**: legal_action作為特徵輸入而不是遮罩使用

### 改進後的架構

## 數據流對比

| 組件 | 原始架構 | 改進架構 |
|------|----------|----------|
| CNN位置 | Preprocessor內部 | 主ActorCriticModel內部 |
| 數據流 | 輸出扁平NumPy向量 | 輸出特徵字典 |
| 模型輸入 | 接收扁平向量 | 接收特徵字典並轉換為Tensor |
| 計算圖 | 被.numpy()切斷 | 完整連續 |
| 梯度流 | 只能在MLP中流動 | 可以流遍整個網絡 |

## 具體改進

### 1. 特徵處理改進
- **移除legal_action作為特徵**: 改為在模型中使用動作遮罩
- **添加閃現狀態特徵**: 3維特徵包含可用性、冷卻時間、移動可用性
- **特徵字典輸出**: 分離地圖特徵和非地圖特徵

### 2. 模型架構改進
- **CNN移至模型內部**: 支持端到端訓練
- **動作遮罩處理**: 在模型中正確處理非法動作
- **特徵重構**: Learner端重新構建特徵字典

### 3. 數據維度變化
```
原始: 2+6+6+6+16+64 = 100維 (扁平向量)
改進: 
  - 地圖特徵: 5×11×11 = 605維 → CNN處理為64維
  - 非地圖特徵: 2+6+6+6+3 = 23維
  - 總計: 87維 (在模型中組合)
```

## 關鍵文件修改

### agent_ppo/feature/preprocessor.py
- 移除CNN特徵提取器
- 輸出特徵字典而非扁平向量
- 添加閃現狀態特徵提取

### agent_ppo/model/model.py
- 添加CNN特徵提取器到主模型
- 修改forward方法處理特徵字典
- NetworkModelLearner重構特徵字典

### agent_ppo/agent.py
- 修改predict_process處理特徵字典
- 更新數據格式轉換

### agent_ppo/feature/definition.py
- SampleManager序列化特徵字典
- 保持與現有訓練流程兼容

### agent_ppo/conf/conf.py
- 更新特徵維度配置
- 反映序列化後的數據維度

## 優勢總結

1. **端到端訓練**: CNN權重可以通過梯度反向傳播更新
2. **狀態感知**: 模型可以感知閃現冷卻等關鍵狀態
3. **高效動作處理**: 使用動作遮罩而非特徵輸入
4. **架構清晰**: 數據流更加清晰，便於調試和優化
5. **性能提升**: 減少特徵維度，提高訓練效率

## 注意事項

- 保持與現有訓練流程的兼容性
- 數據序列化確保learner端正確處理
- 梯度流完整性得到保證
- 所有關鍵狀態信息都被模型感知
